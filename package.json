{"name": "poc-outbox-augment", "version": "1.0.0", "description": "API de Pagamentos com padrão Outbox usando Fastify, TypeScript e SQLite", "main": "dist/app.js", "scripts": {"build": "tsc", "start": "node dist/app.js", "dev": "nodemon --exec ts-node src/app.ts", "dev:watch": "nodemon --watch src --ext ts --exec ts-node src/app.ts", "clean": "rm -rf dist", "prebuild": "npm run clean", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["fastify", "typescript", "sqlite", "api", "pagamentos", "outbox"], "author": "", "license": "ISC", "dependencies": {"@fastify/swagger": "^9.5.1", "@fastify/swagger-ui": "^5.2.3", "better-sqlite3": "^12.2.0", "fastify": "^5.4.0", "pino": "^9.7.0", "pino-pretty": "^13.0.0"}, "devDependencies": {"@types/better-sqlite3": "^7.6.13", "@types/node": "^24.0.13", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}
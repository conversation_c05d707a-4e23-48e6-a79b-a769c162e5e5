import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import { databaseManager } from '../database/database';
import { HealthResponse } from '../types';

export async function healthRoutes(fastify: FastifyInstance) {
  fastify.get('/health', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      // Testar conexão com o banco
      databaseManager.getDatabase().prepare('SELECT 1').get();

      const response: HealthResponse = {
        status: 'ok',
        timestamp: new Date().toISOString(),
        database: 'connected'
      };

      return reply.code(200).send(response);
    } catch (error) {
      const response: HealthResponse = {
        status: 'ok',
        timestamp: new Date().toISOString(),
        database: 'disconnected'
      };

      return reply.code(200).send(response);
    }
  });
}

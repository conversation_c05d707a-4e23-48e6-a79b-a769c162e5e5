export interface CreatePagamentoRequest {
  valor: number;
  descricao: string;
}

export interface UpdatePagamentoRequest {
  valor?: number;
  descricao?: string;
  status?: 'pendente' | 'processado' | 'cancelado';
}

export interface HealthResponse {
  status: 'ok';
  timestamp: string;
  database: 'connected' | 'disconnected';
}

export interface ErrorResponse {
  error: string;
  message: string;
  statusCode: number;
}

# API de Pagamentos - POC Outbox Pattern

API REST desenvolvida com Fastify, TypeScript, SQLite e Swagger para gerenciamento de pagamentos implementando o padrão Outbox.

## 🚀 Tecnologias

- **Node.js 20 LTS**
- **Fastify** - Framework web rápido e eficiente
- **TypeScript** - Tipagem estática
- **SQLite** - Banco de dados leve com better-sqlite3
- **Swagger/OpenAPI** - Documentação automática da API
- **Pino** - Logger de alta performance

## 📋 Funcionalidades

- ✅ Endpoint `/health` para verificação de saúde da aplicação
- ✅ CRUD completo para pagamentos
- ✅ Validação de dados com JSON Schema
- ✅ Documentação automática com Swagger UI
- ✅ Logs estruturados com Pino
- ✅ Banco de dados SQLite com tabela de pagamentos

## 🛠️ Instalação e Execução

### Pré-requisitos
- Node.js 20 LTS ou superior
- npm ou yarn

### Instalação
```bash
# Instalar dependências
npm install
```

### Execução

#### Desenvolvimento (com hot reload)
```bash
npm run dev
```

#### Produção
```bash
# Build do projeto
npm run build

# Executar versão compilada
npm start
```

## 📚 Endpoints da API

### Health Check
- `GET /health` - Verifica se a API está funcionando e se o banco está conectado

### Pagamentos
- `GET /pagamentos` - Lista todos os pagamentos
- `GET /pagamentos/:id` - Busca um pagamento específico
- `POST /pagamentos` - Cria um novo pagamento
- `PUT /pagamentos/:id` - Atualiza um pagamento existente
- `DELETE /pagamentos/:id` - Remove um pagamento

### Documentação
- `GET /docs` - Interface Swagger UI com documentação interativa

## 📊 Estrutura do Banco de Dados

### Tabela: pagamentos
```sql
CREATE TABLE pagamentos (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  valor REAL NOT NULL,
  descricao TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'pendente',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**Status possíveis:**
- `pendente` - Pagamento criado, aguardando processamento
- `processado` - Pagamento processado com sucesso
- `cancelado` - Pagamento cancelado

## 🔧 Configuração

### Variáveis de Ambiente
- `PORT` - Porta do servidor (padrão: 3000)
- `HOST` - Host do servidor (padrão: 0.0.0.0)

### Exemplo de uso
```bash
PORT=8080 HOST=localhost npm run dev
```

## 📝 Exemplos de Uso

### Criar um pagamento
```bash
curl -X POST http://localhost:3000/pagamentos \
  -H "Content-Type: application/json" \
  -d '{
    "valor": 100.50,
    "descricao": "Pagamento de teste"
  }'
```

### Listar pagamentos
```bash
curl http://localhost:3000/pagamentos
```

### Verificar saúde da API
```bash
curl http://localhost:3000/health
```

## 🏗️ Estrutura do Projeto

```
src/
├── database/
│   └── database.ts      # Configuração e operações do SQLite
├── routes/
│   ├── health.ts        # Rotas de health check
│   └── pagamentos.ts    # Rotas CRUD de pagamentos
├── types/
│   └── index.ts         # Definições de tipos TypeScript
└── app.ts               # Configuração principal do Fastify
```

## 🚀 Próximos Passos

Para implementar o padrão Outbox completo, considere adicionar:

1. **Tabela Outbox** - Para armazenar eventos a serem publicados
2. **Event Publisher** - Serviço para publicar eventos da outbox
3. **Transações** - Garantir consistência entre pagamentos e eventos
4. **Message Broker** - Integração com RabbitMQ, Kafka, etc.
5. **Testes** - Testes unitários e de integração

## 📄 Licença

ISC
